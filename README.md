# ngTool - Nginx 服务管理器

一个现代化的 nginx 服务管理工具，使用纯 C++ 和原生 Windows API 开发。

## ✨ 特性

- 🚀 **零依赖**: 不依赖任何外部 GUI 框架
- 🎯 **现代化界面**: 双行按钮布局，紧凑高效
- 🌏 **完整中文支持**: Unicode 编码，完美显示中文
- ⚡ **轻量级**: 仅 ~200KB 的可执行文件
- 🎨 **自定义字体**: 支持字体大小调整和实时预览
- 🔧 **易于使用**: 直观的操作界面和功能
- 🖼️ **应用程序图标**: 专业的应用程序图标显示

## 🎮 功能

### 核心功能
- ✅ nginx 服务启动/停止/重启
- ✅ 实时服务状态监控 (彩色状态指示)
- ✅ nginx 路径配置和验证
- ✅ 配置文件快速编辑
- ✅ 详细操作日志记录 (彩色日志)
- ✅ 配置自动保存和恢复

### 界面特色
- 🎨 **字体设置对话框**: 独立调整普通文本、按钮文本、日志文本字体大小
- 📱 **双行按钮布局**: 紧凑的按钮排列，适应更多功能
- 🔤 **优化的字体渲染**: 使用 Microsoft YaHei UI 和 Consolas 字体
- 🎯 **Emoji 图标按钮**: 直观的按钮识别
- 📝 **状态文本修复**: 解决了文本重叠显示问题
- 🖼️ **应用程序图标**: 在文件管理器、任务栏、标题栏中显示图标

## 🛠️ 技术栈

- **语言**: C++17
- **API**: Windows API (User32, Kernel32, Shell32)
- **编码**: Unicode (UTF-16) 支持
- **编译器**: MinGW, MSVC, Clang
- **架构**: 单文件设计

## 🚀 快速开始

### 直接使用
```bash
# 直接运行预编译版本
ngTool.exe
```

### 从源码编译
```bash
# 使用提供的构建脚本 (推荐)
build.bat

# 或手动编译
cd src
windres resource.rc -o resource.o
g++ -O2 -s -mwindows -o ngTool.exe simple-main.cpp resource.o -lgdi32 -luser32 -lkernel32 -lshell32 -lole32
```

## 📁 项目结构

```
nginx-manager/
├── src/
│   ├── simple-main.cpp     # 主程序源码
│   ├── resource.rc         # Windows 资源文件
│   ├── resource.h          # 资源头文件
│   ├── icon.ico           # 应用程序图标
│   └── create_icon.c      # 图标生成工具
├── ngTool.exe             # 编译后的可执行文件
├── nginx-manager.ini      # 配置文件 (运行时生成)
├── build.bat              # 自动构建脚本
├── README.md              # 项目说明
├── IMPROVEMENTS_COMPLETED.md  # 改进完成报告
├── 使用说明.md            # 详细使用指南
└── LICENSE                # MIT 许可证
```

## 🎨 界面预览

```
┌─────────────────────────────────────────────────────────┐
│ 🖼️ Nginx 管理器                                [_][□][×]│
├─────────────────────────────────────────────────────────┤
│ Nginx 安装路径:                                         │
│ [D:\nginx-1.26.3                      ] [浏览]         │
│ 服务状态: 运行中                                        │
├─────────────────────────────────────────────────────────┤
│ [🚀启动服务] [⏹️停止服务] [🔄重启服务] [🔍刷新状态]      │
│ [⚙️打开配置] [🎨字体设置]                               │
├─────────────────────────────────────────────────────────┤
│ 操作日志:                                               │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ [15:30:21] 配置已保存                               │ │
│ │ [15:30:21] 已加载配置: D:\nginx-1.26.3              │ │
│ │ [15:30:21] Nginx 管理器已启动                       │ │
│ │ [15:30:33] 正在启动 nginx...                        │ │
│ │ [15:30:36] ✓ Nginx 启动成功                         │ │
│ │ [15:31:15] 字体设置已保存，重启应用程序以完全生效    │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 💡 使用方法

1. **设置路径**: 在路径输入框中设置 nginx 安装目录
2. **管理服务**: 使用第一行按钮控制 nginx 服务状态
3. **编辑配置**: 点击"⚙️打开配置"按钮编辑 nginx.conf
4. **字体设置**: 点击"🎨字体设置"按钮调整界面字体大小
5. **查看日志**: 在日志区域查看详细的彩色操作记录
6. **自动保存**: 所有设置会自动保存，下次启动时恢复

## 🔧 系统要求

- **操作系统**: Windows 7 或更高版本
- **编译环境**: 支持 C++17 的编译器
- **运行时**: 无额外依赖，开箱即用

## 📖 详细文档

- [使用说明.md](使用说明.md) - 详细的使用指南和编译说明
