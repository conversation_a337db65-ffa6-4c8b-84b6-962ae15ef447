# Nginx 管理器使用说明

## 概述

这是一个使用原生 Windows API 开发的 nginx 服务管理工具，具有以下特点：

- **零依赖**: 不需要安装任何外部库或框架
- **轻量级**: 编译后的可执行文件小于 1MB
- **原生体验**: 使用 Windows 原生控件，完美融入系统
- **现代界面**: 支持字体设置、双行按钮布局、应用程序图标
- **易于编译**: 只需要基本的 C++ 编译器

## 项目结构

```
nginx-manager/
├── src/
│   ├── simple-main.cpp     # 主程序源代码
│   ├── resource.rc         # Windows 资源文件
│   ├── resource.h          # 资源头文件
│   ├── icon.ico           # 应用程序图标
│   └── create_icon.c      # 图标生成工具
├── build.bat              # 自动编译脚本
├── ngTool.exe             # 编译后的可执行文件
├── nginx-manager.ini      # 配置文件
└── README.md              # 项目说明
```

## 编译方法

### 自动编译 (推荐)

```bash
build.bat
```

脚本会自动编译资源文件和主程序。

### 手动编译

#### 步骤1: 生成图标文件 (如果需要)

```bash
cd src
gcc create_icon.c -o create_icon.exe
./create_icon.exe
```

#### 步骤2: 编译资源文件

```bash
cd src
windres resource.rc -o resource.o
```

#### 步骤3: 编译主程序

使用 g++ (MinGW):
```bash
cd src
g++ -o ngTool.exe simple-main.cpp resource.o -lgdi32 -luser32 -lkernel32 -lshell32 -lole32 -mwindows
```

使用 cl.exe (Visual Studio):
```bash
cd src
rc resource.rc
cl /MT simple-main.cpp resource.res /Fe:ngTool.exe user32.lib gdi32.lib kernel32.lib shell32.lib ole32.lib
```

## 功能说明

### 1. 路径配置

- 在顶部文本框中输入或选择 nginx 安装目录
- 点击"浏览"按钮可以通过文件夹选择对话框选择路径
- 路径会自动保存到配置文件 `nginx-manager.ini`

### 2. 服务控制 (第一行按钮)

- **🚀 启动服务**: 启动 nginx 服务 (需要有效的 nginx 路径)
- **⏹️ 停止服务**: 强制停止所有 nginx 进程
- **🔄 重启服务**: 先停止再启动 nginx 服务
- **🔍 刷新状态**: 手动刷新服务状态

### 3. 配置和工具 (第二行按钮)

- **⚙️ 打开配置**: 使用默认编辑器打开 nginx.conf 配置文件
- **🎨 字体设置**: 打开字体设置对话框，可调整界面字体大小

### 4. 字体设置功能

- 支持独立设置普通文本、按钮文本、日志文本的字体大小
- 字体大小范围：8-72 点
- 实时预览功能
- 设置自动保存到配置文件

### 5. 状态监控

- 状态栏显示当前 nginx 运行状态
- 支持彩色状态指示：绿色(运行中)、红色(已停止)、橙色(处理中)
- 状态文本重叠问题已修复

### 6. 操作日志

- 底部日志区域显示所有操作的详细记录
- 支持彩色日志：不同操作类型使用不同颜色
- 包含时间戳和操作结果
- 使用等宽字体 (Consolas) 提高可读性

## 界面布局

```
┌─────────────────────────────────────────────────────────┐
│ Nginx 管理器                                    [_][□][×]│
├─────────────────────────────────────────────────────────┤
│ Nginx 安装路径:                                         │
│ [D:\nginx-1.26.3                             ] [浏览]  │
│ 服务状态: 运行中                                        │
├─────────────────────────────────────────────────────────┤
│ [🚀启动服务] [⏹️停止服务] [🔄重启服务] [🔍刷新状态]      │
│ [⚙️打开配置] [🎨字体设置]                               │
├─────────────────────────────────────────────────────────┤
│ 操作日志:                                               │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ [15:30:21] 配置已保存                               │ │
│ │ [15:30:21] 已加载配置: D:\nginx-1.26.3              │ │
│ │ [15:30:21] Nginx 管理器已启动                       │ │
│ │ [15:30:25] 正在停止 nginx...                        │ │
│ │ [15:30:27] ✓ Nginx 停止成功                         │ │
│ │ [15:30:31] Nginx 未运行                             │ │
│ │ [15:30:33] 正在启动 nginx...                        │ │
│ │ [15:30:36] ✓ Nginx 启动成功                         │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 配置文件

程序会在当前目录创建 `nginx-manager.ini` 配置文件，用于保存：

- nginx 安装路径
- 字体设置 (普通文本、按钮文本、日志文本)

配置文件格式：
```ini
[Settings]
NginxPath=D:\nginx-1.26.3

[Fonts]
TitleSize=24
NormalSize=16
ButtonSize=15
LogSize=12
```

## 系统要求

- **操作系统**: Windows 7 或更高版本
- **编译器**: 支持 C++17 的编译器 (g++, cl.exe, clang++)
- **依赖**: 仅需要 Windows SDK (通常随编译器提供)

## 注意事项

### 权限要求

- 启动 nginx 通常不需要管理员权限
- 停止 nginx 进程可能需要管理员权限
- 如果遇到权限问题，请以管理员身份运行程序

### nginx 路径要求

- 路径必须指向包含 `nginx.exe` 的目录
- 确保 nginx 配置文件 (`nginx.conf`) 语法正确
- 检查端口是否被其他程序占用

### 故障排除

1. **启动失败**:
   - 检查 nginx 路径是否正确
   - 检查 nginx 配置文件语法
   - 查看 nginx 错误日志

2. **停止失败**:
   - 尝试以管理员身份运行程序
   - 手动使用任务管理器终止进程

3. **界面异常**:
   - 重启程序
   - 删除配置文件重新设置

## 技术特点

### 单文件架构

- 所有功能集中在一个 `main.cpp` 文件中
- 便于理解、修改和维护
- 减少编译复杂度

### 原生 Windows API

- 使用 `CreateWindow` 创建界面控件
- 使用 `ListView` 显示进程列表
- 使用 `GetPrivateProfileString` 管理配置
- 使用 `CreateToolhelp32Snapshot` 枚举进程

### 现代 C++ 特性

- 使用 `std::wstring` 处理 Unicode 字符串
- 使用 `std::vector` 管理动态数据
- 支持 C++17 标准

## 扩展开发

如果需要添加新功能，可以在 `main.cpp` 中：

1. 添加新的控件 ID 定义
2. 在 `CreateControls` 函数中创建新控件
3. 在 `WindowProc` 函数中处理新的消息
4. 添加相应的功能函数

